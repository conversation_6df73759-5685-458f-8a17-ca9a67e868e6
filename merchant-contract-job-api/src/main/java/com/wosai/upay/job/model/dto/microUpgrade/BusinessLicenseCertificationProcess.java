package com.wosai.upay.job.model.dto.microUpgrade;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description: 营业执照认证流程展示
 * <AUTHOR>
 * @Date 2025/7/3 11:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessLicenseCertificationProcess {

    /**
     * 步骤序号（1,2,3,4）
     */
    private Integer stepNumber;

    /**
     * 节点类型
     */
    private NodeType type;

    /**
     * 步骤显示名称
     */
    private String showName;

    /**
     * 是否显示该步骤
     */
    private Boolean show;

    /**
     * 步骤状态：done, processing, pending, failed
     */
    private String status;

    /**
     * 是否已完成
     */
    private Boolean finished;

    /**
     * 是否为当前进行中的步骤
     */
    private Boolean current;

    /**
     * 提示消息（如：请等待1个工作日）
     */
    private String promptMessage;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 完成时间显示文本（如：2025-07-01 14:40:27）
     */
    private String finishTimeText;

    /**
     * 业务类型
     */
    private BusinessType businessType;

    /**
     * 节点类型枚举
     */
    public enum NodeType {
        SUBMIT_CERT("提交认证"),
        ACQUIRER_REVIEW("收单机构审核中"),
        SOURCE_MERCHANT_AUTH("支付源商家认证"),
        PAYMENT_AUTH("微信/支付宝收款授权");

        private final String description;

        NodeType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 业务类型枚举
     */
    public enum BusinessType {
        MICRO_UPGRADE_V2("小微升级V2"),
        MICRO_UPGRADE_V3("小微升级V3"),
        LICENSE_UPDATE("营业执照更新"),
        BANK_ACCOUNT_CHANGE("银行账户变更");

        private final String description;

        BusinessType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 流程步骤状态常量
     */
    public static class ProcessStatus {
        public static final String DONE = "done";
        public static final String PROCESSING = "processing";
        public static final String PENDING = "pending";
        public static final String FAILED = "failed";
    }
}
